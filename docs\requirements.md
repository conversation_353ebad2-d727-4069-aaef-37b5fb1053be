# 爬虫系统服务端需求

## 核心功能

### 1. 用户系统

- **注册**: 邀请码注册(可选，也可直接注册)
- **登录**: 用户名密码登录，返回 JWT token
- **用户信息**: 查看个人信息和佣金余额
- **邀请码管理**: 用户可生成和管理自己的邀请码

### 2. 任务系统

- **获取任务**: 客户端拉取爬虫任务(商品 ID)
- **上报结果**: 客户端提交爬取的商品数据

### 3. Cookie 管理

- **上报 Cookie**: 用户提交可用的 Cookie
- **有效性判断**: 根据任务执行结果自动判断 Cookie 是否有效

### 4. 佣金系统

- **动态佣金计算**: 任务佣金 = 基准价格 + 活跃邀请人数 × 比例
- **活跃邀请人**: 只有完成过任务的被邀请人才计入佣金加成
- **查询佣金**: 用户查看佣金明细
- **结算佣金**: 记录结算(不集成支付)

### 5. 推荐系统 (直接推荐)

- **推荐注册**: 用户生成推荐码，邀请新用户注册
- **推荐关系**: 只记录直接的推荐人和被推荐人关系，不建立多级关系链
- **佣金加成机制**:
  - 推荐人完成任务时，根据活跃邀请人数获得佣金加成
  - 活跃邀请人：完成过至少一个任务的被推荐人
  - 被推荐人的佣金不受影响，推荐人获得额外加成
- **团队统计**: 查看直接推荐用户的活跃情况

## 技术选型

- FastAPI + SQLAlchemy + SQLite
- JWT 认证
- 复用现有爬虫 API(天猫/京东)

## API 接口

### 用户接口

- `POST /register` - 注册用户
- `POST /login` - 用户登录
- `GET /user/info` - 获取用户信息

### 任务接口

- `GET /tasks/pull` - 拉取任务
- `POST /tasks/submit` - 提交结果

### Cookie 接口

- `POST /cookies/upload` - 上报 Cookie
- `GET /cookies/list` - 查看自己的 Cookie 列表和状态

### 佣金接口

- `GET /commission/balance` - 查询余额
- `GET /commission/history` - 佣金明细

### 推荐返佣接口

- `POST /referral/code` - 生成新的邀请码
- `GET /referral/codes` - 查看自己的邀请码列表
- `GET /referral/team` - 查看推荐的用户列表
- `GET /referral/commission` - 返佣收益明细

## 返佣规则

### 推荐关系

- 用户注册时填写推荐码，建立直接推荐关系
- 只记录一对一的推荐关系: 推荐人 → 被推荐人
- 不建立多级推荐链，避免传销风险
- 推荐关系一旦建立不可更改

### 佣金加成规则 (示例)

- **基准价格**: 每个平台设定基础价格(如淘宝 1.0 元)
- **加成比例**: 每个活跃邀请人增加 0.1 元
- **计算示例**: 用户邀请5人，其中3人活跃，任务佣金 = 1.0 + 3 × 0.1 = 1.3 元
- **互利共赢**: 被邀请人完成任务产生价值，推荐人才能享受加成

### 合规说明

- 只有直接推荐关系，无多级分销
- 返佣基于实际任务完成，非拉人头
- 推荐奖励来源于平台，不影响被推荐人收益
- 完全避免传销风险，遵守相关法律法规

## 任务导入格式

### Excel/CSV 格式
必需字段：
- `platform` - 平台代码(tmall, jd, pdd等)
- `item_id` - 商品ID

可选字段：
- `task_type` - 任务类型(默认BasicInfo)

注：任务价格和佣金由平台配置自动确定

### JSON 格式
```json
[
  {
    "platform": "tmall",
    "item_id": "123456789",
    "task_type": "BasicInfo"
  }
]
```

## 管理端接口

### 管理员认证

- `POST /admin/login` - 管理员登录
- `POST /admin/logout` - 管理员登出

### 用户管理

- `GET /admin/users` - 用户列表(分页、搜索、筛选)
- `GET /admin/users/{user_id}` - 用户详情
- `PUT /admin/users/{user_id}/status` - 修改用户状态(启用/禁用)
- `GET /admin/invites` - 查看所有邀请码使用情况

### 平台管理

- `GET /admin/platforms` - 平台列表
- `POST /admin/platforms` - 创建新平台
- `PUT /admin/platforms/{platform_id}` - 修改平台信息(价格、状态等)
- `DELETE /admin/platforms/{platform_id}` - 删除平台

### 任务管理

- `POST /admin/tasks` - 单个创建任务
- `POST /admin/tasks/import` - 批量导入任务(支持Excel/CSV/JSON)
- `GET /admin/tasks` - 任务列表(状态筛选、分页)
- `PUT /admin/tasks/{task_id}` - 修改任务状态
- `DELETE /admin/tasks/{task_id}` - 删除任务
- `GET /admin/tasks/stats` - 任务统计数据
- `GET /admin/tasks/imports` - 导入记录列表
- `GET /admin/tasks/imports/{batch_id}` - 查看导入详情

### Cookie 管理

- `GET /admin/cookies` - Cookie 列表(按用户、平台筛选)
- `PUT /admin/cookies/{cookie_id}/status` - 手动标记 Cookie 状态
- `DELETE /admin/cookies/{cookie_id}` - 删除 Cookie 记录

### 佣金管理

- `GET /admin/commissions` - 佣金记录列表
- `PUT /admin/commissions/{commission_id}` - 修改佣金状态
- `POST /admin/settlements` - 创建结算批次
- `GET /admin/settlements` - 结算记录列表

### 推荐管理

- `GET /admin/referrals` - 查看用户推荐关系
- `GET /admin/referrals/stats` - 推荐统计数据
- `PUT /admin/referrals/{user_id}/commission_rate` - 调整用户返佣比例

### 系统管理

- `GET /admin/stats/dashboard` - 系统概览数据
- `GET /admin/logs` - 系统日志
- `PUT /admin/settings` - 系统配置(返佣比例、任务佣金等)
