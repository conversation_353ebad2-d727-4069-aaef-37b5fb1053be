from api.tmall.item import get_item_info
import time
import random
cookies = {
    'cookie2': '1fe746af0805cd034ddae4a58ef2f2b7',
    'wk_cookie2': '18512dd28a0af37386a924bf00962ba9',
    'wk_unb': 'UUpjNmDHQUIX3xF%2BfQ%3D%3D',
}
if __name__ == "__main__":
    i = 0
    while True:
        delay = random.uniform(30,60)
        
        print(f'{delay}秒后，发起第{i+1}次请求')
        time.sleep(delay)
        print(get_item_info(item_id='903877239950',cookies=cookies))
        i += 1