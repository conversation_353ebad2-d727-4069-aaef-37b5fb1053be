# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is an e-commerce product crawler that supports multiple platforms (Tmall and JD). The project uses Python with `uv` as the package manager and focuses on extracting product information from major Chinese e-commerce platforms.

## Development Commands

- **Install dependencies**: `uv sync`
- **Run the main crawler**: `uv run python main.py`
- **Add new dependencies**: `uv add <package_name>`
- **Remove dependencies**: `uv remove <package_name>`

## Architecture

### Core Structure

- `client/api/` - Platform-specific API implementations
  - `tmall/` - Tmall/Taobao crawling functionality
  - `jd/` - JingDong (JD.com) crawling functionality
- `server/` - Server-side components (currently empty)
- `main.py` - Entry point with crawling loop

### API Layer Design

Each platform follows a consistent interface pattern:

- `item.py` - Contains `get_item_info(item_id, cookies)` function
- Returns standardized response: `{"success": bool, "data": dict|None, "msg": str}`

### Platform Implementations

**Tmall/Taobao (`client/api/tmall/`)**:

- Uses Taobao's H5 API endpoints
- Requires cookie management with automatic token refresh
- Implements MD5 signature verification for API calls
- Handles both item detail retrieval and task management

**JingDong (`client/api/jd/`)**:

- Scrapes mobile JD pages and extracts JavaScript data
- Requires `shshshfpx` and `pt_key` cookies for authentication
- Uses regex to extract `window._itemOnly` and `window._itemInfo` data
- Parses JavaScript objects with `json5` library

### Dependencies

- `curl-cffi` - HTTP client with browser-like behavior
- `drissionpage` - Web automation (available but not currently used)
- `json5` - JavaScript object parsing for JD data extraction

### Security Considerations

- Cookie management is critical for both platforms
- Rate limiting implemented in main loop (30-60 second delays)
- API signatures used for Tmall authentication
- Never commit actual cookies or tokens to the repository
